from onyx.configs.app_configs import E<PERSON><PERSON><PERSON><PERSON><PERSON>_KEY_SECRET
from onyx.utils.logger import setup_logger
from onyx.utils.variable_functionality import fetch_versioned_implementation
from functools import lru_cache
from os import urandom

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes

logger = setup_logger()


# def _encrypt_string(input_str: str) -> bytes:
#     if ENCRYPTION_KEY_SECRET:
#         logger.warning("MIT version of Onyx does not support encryption of secrets.")
#     return input_str.encode()


# def _decrypt_bytes(input_bytes: bytes) -> str:
#     # No need to double warn. If you wish to learn more about encryption features
#     # refer to the Onyx EE code
#     return input_bytes.decode()


# def encrypt_string_to_bytes(intput_str: str) -> bytes:
#     versioned_encryption_fn = fetch_versioned_implementation(
#         "onyx.utils.encryption", "_encrypt_string"
#     )
#     return versioned_encryption_fn(intput_str)


# def decrypt_bytes_to_string(intput_bytes: bytes) -> str:
#     versioned_decryption_fn = fetch_versioned_implementation(
#         "onyx.utils.encryption", "_decrypt_bytes"
#     )
#     return versioned_decryption_fn(intput_bytes)

@lru_cache(maxsize=1)
def _get_valid_aes_key(key: str) -> bytes:
    """Ensures the key length is valid for AES (16, 24, or 32 bytes)."""
    key_bytes = key.encode()
    if len(key_bytes) < 16:
        raise ValueError("Encryption key is too short. Must be at least 16 bytes.")
    
    valid_lengths = [16, 24, 32]
    closest = min(valid_lengths, key=lambda x: abs(len(key_bytes) - x))
    return key_bytes[:closest]


def encrypt_string_to_bytes(plaintext: str) -> bytes:
    """Encrypt a string using AES-CBC with PKCS7 padding."""
    if not ENCRYPTION_KEY_SECRET:
        raise RuntimeError("Encryption key not configured.")

    key = _get_valid_aes_key(ENCRYPTION_KEY_SECRET)
    iv = urandom(16)

    padder = padding.PKCS7(algorithms.AES.block_size).padder()
    padded = padder.update(plaintext.encode()) + padder.finalize()

    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
    encryptor = cipher.encryptor()
    ciphertext = encryptor.update(padded) + encryptor.finalize()

    return iv + ciphertext  # prepend IV for use in decryption


def decrypt_bytes_to_string(ciphertext: bytes) -> str:
    """Decrypt bytes encrypted with AES-CBC and PKCS7 padding."""
    if not ENCRYPTION_KEY_SECRET:
        raise RuntimeError("Encryption key not configured.")

    key = _get_valid_aes_key(ENCRYPTION_KEY_SECRET)
    iv, encrypted_data = ciphertext[:16], ciphertext[16:]

    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
    decryptor = cipher.decryptor()
    padded_plaintext = decryptor.update(encrypted_data) + decryptor.finalize()

    unpadder = padding.PKCS7(algorithms.AES.block_size).unpadder()
    plaintext = unpadder.update(padded_plaintext) + unpadder.finalize()

    return plaintext.decode()